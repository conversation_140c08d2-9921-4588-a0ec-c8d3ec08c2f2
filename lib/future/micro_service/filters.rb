module Future
  module MicroService
    Filter = Value.new(:segment_class_id, :segment_ids, :self_report) {
      def self.for_report_filter(report_filter)
        return nil if report_filter.nil? || report_filter.is_a?(AllResultsReportFilter)

        stq = report_filter.survey_to_question
        Filter.with(
          segment_class_id: stq.id,
          segment_ids: [report_filter.option.id],
          self_report: stq.self_report?
        )
      end
    }

    Filters = Value.new(:filters, :hierarchy_filters) {
      def self.for_context(context, full_reporting_line)
        with(
          filters: direct_filters(anchors: Array.wrap(context.anchors), filters: context.filters, full_reporting_line: full_reporting_line),
          hierarchy_filters: hierarchy_filters(context.anchors, full_reporting_line, context.leader_filter)
        )
      end

      def self.deserialize(filters_hash)
        with(
          filters: deserialize_filters(filters_hash[:filters]),
          hierarchy_filters: deserialize_filters(filters_hash[:hierarchy_filters])
        )
      end

      # @return [Hash] with keys `filters` and `hierarchy_filters`
      def serialize
        {
          filters: serialize_filters(filters),
          hierarchy_filters: serialize_filters(hierarchy_filters)
        }
      end

      def self.deserialize_filters(filters_hash)
        filters_hash.map do |filter_stq_id, filter_option_ids|
          Filter.with(
            segment_class_id: filter_stq_id,
            segment_ids: filter_option_ids,
            self_report: false
          )
        end
      end

      def serialize_filters(filters)
        filters.map { |filter|
          [filter.segment_class_id.to_s, filter.segment_ids.map(&:to_s)]
        }.to_h
      end

      # Construct a (singleton) list of the hierarchy filters applicable out of the `anchors` and the `leader_filter`
      #
      # Example use - this is used to serialize filters for creating `Jobs::TextAnalytics::Export` jobs
      #
      # @param anchors [Array<ReportFilter>, nil] the report anchors
      # @param full_reporting_line [Boolean] true if the report is a leader-based report with `full reporting line` enabled.  (i.e. it is a hierarchical report)
      # @param leader_filter [ReportFilter, nil] the selected leader, if one is chosen in the leader filter.
      # @return Array<MicroService::Filter>
      def self.hierarchy_filters(anchors, full_reporting_line, leader_filter)
        return [Filter.for_report_filter(leader_filter)] if leader_filter.present?
        return [] unless anchors.present? && hierarchy_anchored_report?(anchors: anchors, full_reporting_line: full_reporting_line)
        [Filter.for_report_filter(anchors.first)]
      end

      # Collect together the non-hierarchy filters out of `filters`.
      #
      # This method assumes that the only hierarchy filter is the anchor.  This is in keeping with `#hierarchy_filters`
      # above which assumes that the `anchor` is the only possible hierarchical filter.
      #
      # NOTE: THIS MAY NOT BE CORRECT.  IF LEGACY HIERARCHY FILTERS ARE BROKEN IN COMMENTS REPORTS, IT MAY BE DUE TO THIS ASSUMPTION.
      # IN THAT CASE, BOTH `hierarchy_filters` AND `non_hierarchy_filters` NEED TO BE UPDATED.
      #
      # @param anchors [Array<ReportFilter>, nil]
      # @param filters [Array<ReportFilter>]
      # @param full_reporting_line [boolean] True if the report is a hierarchy report.
      # @return Array<ReportFilter>
      def self.non_hierarchy_filters(anchors:, filters:, full_reporting_line:)
        hierarchy_anchor = anchors.first if anchors.present? && hierarchy_anchored_report?(anchors: anchors, full_reporting_line: full_reporting_line)
        filters.reject { |filter| filter.is_a?(AllResultsReportFilter) || filter == hierarchy_anchor }
      end

      # Collect together the non-hierarchy filters out of `filters`.
      #
      # See `MicroService#non_hierarchy_filters`
      #
      # @param anchors [Array<ReportFilter>, nil]
      # @param filters [Array<ReportFilter>]
      # @param full_reporting_line [boolean] True if the report is a hierarchy report.
      # @return Array<MicroService::Filter>
      def self.direct_filters(anchors:, filters:, full_reporting_line:)
        filters_hash = non_hierarchy_filters(anchors: anchors, filters: filters, full_reporting_line: full_reporting_line).group_by(&:survey_to_question)
        filters_hash.map do |stq, filters|
          Filter.with(
            segment_class_id: stq.id,
            segment_ids: filters.map(&:option).map(&:id),
            self_report: stq.self_report?
          )
        end
      end

      private

      # Determine whether a report has a hierarchy anchor or not.  There are two determinants of a hierarchy anchor:
      #
      # 1. `anchor.heirarchy? == true`.
      #    Legacy reports anchored on a `SurveyToQuestion` containing a `SegmentHierarchy`.  In this case we just _assume_
      #    that the anchor is being used for hierarchical reporting.
      # 2. `full_reporting_line`.
      #    "Modern" leader-based shared reports with the `full reporting line` option selected.  In this case, we _know_
      #    that the anchor is intended to be used for hierarchical reporting.
      #
      # NOTE: This could be improved by making `ReportFilter.hierarchy?` always true if the filter is intended to be
      # hierarchical (rather than just in the legacy case).
      def self.hierarchy_anchored_report?(anchors:, full_reporting_line:)
        # NOTE: `anchor.hierarchy` only returns true for an anchor on a `SurveyToQuestion` which contains a `SegmentHierarchy`.
        anchors.any? { |anchor| anchor.hierarchy? } || full_reporting_line
      end
    }
  end
end
