require "rails_helper"

RSpec.describe SharedReports::Queries::HierarchyPermissions do
  subject { described_class.new.call(user: user, report: report, anchor: anchor) }

  let(:survey) { FactoryBot.create(:rich_survey, survey_to_questions: [hierarchy_stq]) }
  let(:hierarchy_question) { FactoryBot.create(:segment_question, name: "Department", hierarchy_type: :employee, select_options: []) }
  let(:hierarchy_stq) { SurveyToQuestion.new(question: hierarchy_question, type: hierarchy_question.type) }
  let!(:employee_hierarchy) do
    survey.employee_hierarchies.create!(
      hierarchy_question: hierarchy_question,
      select_option_to_parent: {
        employee_select_option.id.to_s => manager1_select_option.id.to_s,
        manager1_select_option.id.to_s => director_select_option.id.to_s,
        survey_admin_select_option.id.to_s => director_select_option.id.to_s,
        hrbp_select_option.id.to_s => survey_admin_select_option.id.to_s
      },
      levels: {"0" => [director_select_option.id.to_s],
               "1" => [manager1_select_option.id.to_s, survey_admin_select_option.id.to_s],
               "2" => [employee_select_option.id.to_s, hrbp_select_option.id.to_s]}
    )
  end
  let(:employee) { FactoryBot.create(:user, account: survey.account, name: "Mr. Employee") }
  let(:manager1) { FactoryBot.create(:user, account: survey.account, name: "Mr. Manager1") }
  let(:director) { FactoryBot.create(:user, account: survey.account, name: "Miss. Director") }
  let(:hrbp) { FactoryBot.create(:hr_business_partner, account: survey.account, name: "Ms HRBP") }
  let(:survey_admin) { FactoryBot.create(:admin, account: survey.account, name: "Survey Admin") }
  let(:employee_select_option) { FactoryBot.create(:select_option, select_question: hierarchy_question, employee_aggregate_id: employee.aggregate_id, short_label: employee.name) }
  let(:manager1_select_option) { FactoryBot.create(:select_option, select_question: hierarchy_question, employee_aggregate_id: manager1.aggregate_id, short_label: manager1.name) }
  let(:director_select_option) { FactoryBot.create(:select_option, select_question: hierarchy_question, employee_aggregate_id: director.aggregate_id, short_label: director.name) }
  let(:hrbp_select_option) { FactoryBot.create(:select_option, select_question: hierarchy_question, employee_aggregate_id: hrbp.aggregate_id, short_label: hrbp.name) }
  let(:survey_admin_select_option) { FactoryBot.create(:select_option, select_question: hierarchy_question, employee_aggregate_id: survey_admin.aggregate_id, short_label: survey_admin.name) }

  let(:user) { director }

  let(:report_sharing_status) { "published" }
  let(:direct_reports_only) { false }
  let(:report_parameters) do
    {
      name: "Level 0 Hierarchy Report",
      description: "with report viewers",
      base_demographic_stq_id: hierarchy_stq.id,
      report_view: "standard",
      direct_reports_only: direct_reports_only,
      hierarchy_levels: [0],
      is_hierarchy_report: true,
      sharing_status: report_sharing_status
    }
  end
  let(:report) { Services::SharedReportsService.create_report(survey, report_parameters, true) }

  shared_examples "hierarchy permissions" do
    context "when they do not have a report access grant" do
      let(:anchor) { nil }
      it { is_expected.to be false }
    end

    context "when they do have a report access grant" do
      let(:anchor) { "#{hierarchy_stq.id}-#{select_option.id}" }
      before do
        report.access_grants.create!(
          report_consumer: user,
          select_option_id: select_option.id,
          select_option_ids: [select_option.id]
        )
      end

      it { is_expected.to be true }

      context "report doesn't allow direct line" do
        let(:direct_reports_only) { true }
        it { is_expected.to be false }
      end

      context "when report is unpublished" do
        let(:report_sharing_status) { "unpublished" }
        it { is_expected.to eq can_view_unpublished }
      end
    end
  end

  before do
    hierarchy_stq.update_attributes(employee_hierarchy_id: employee_hierarchy.id)
  end

  context "when the user is a manager (a director)" do
    let(:user) { director }
    let(:select_option) { director_select_option }
    let(:can_view_unpublished) { false }
    it_behaves_like "hierarchy permissions"
  end

  context "when the user has HRBP permissions" do
    let(:user) { hrbp }
    let(:select_option) { director_select_option }
    let(:can_view_unpublished) { true }
    it_behaves_like "hierarchy permissions"
  end

  context "when user is survey admin" do
    let(:user) { survey_admin }
    let(:select_option) { director_select_option }
    let(:can_view_unpublished) { true }
    it_behaves_like "hierarchy permissions"
  end

  context "when user is an IC" do
    let(:user) { employee }
    let(:select_option) { director_select_option }
    let(:can_view_unpublished) { false }
    it_behaves_like "hierarchy permissions"
  end

  context "when user is not in the hierarchy at all" do
    let(:user) { FactoryBot.create(:user, account: survey.account) }
    let(:select_option) { director_select_option }
    let(:can_view_unpublished) { false }
    it_behaves_like "hierarchy permissions"
  end
end
