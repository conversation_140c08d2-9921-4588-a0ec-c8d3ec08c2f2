require "rails_helper"

RSpec.describe Future::MicroService::Filters do
  subject { Future::MicroService::Filters.for_context(context, full_reporting_line) }
  let(:context) { instance_double(Context, filters: report_filters, anchors: [hierarchy_filter], leader_filter: nil) }
  let(:report_filters) { [report_filter_1, report_filter_2] }
  let(:report) { instance_double(Report, full_reporting_line?: roll_up) }

  let(:hierarchy_filter) { instance_double(ReportFilter, survey_to_question: hierarchy_stq, option: hierarchy_option, hierarchy?: true) }
  let(:hierarchy_stq) { instance_double(SurveyToQuestion, id: "hierarchy_stq_id", self_report?: false) }
  let(:hierarchy_option) { instance_double(SelectOption, id: "hierarchy_option_id") }

  let(:report_filter_1) { instance_double(ReportFilter, survey_to_question: stq, option: option1, hierarchy?: false) }
  let(:report_filter_2) { instance_double(ReportFilter, survey_to_question: stq, option: option2, hierarchy?: false) }

  let(:stq) { SurveyToQuestion.new }
  let(:option1) { SelectOption.new }
  let(:option2) { instance_double(SelectOption, id: "option_id_2") }
  let(:full_reporting_line) { false }

  let(:direct_filters) do
    [
      Future::MicroService::Filter.with(
        segment_class_id: stq.id,
        segment_ids: [option1.id, option2.id],
        self_report: false
      )
    ]
  end

  let(:serialized_filters) do
    {
      filters: {stq.id.to_s => [option1.id.to_s, option2.id.to_s]},
      hierarchy_filters: {hierarchy_stq.id.to_s => [hierarchy_option.id.to_s]}
    }
  end

  let(:hierarchy_filters) do
    [
      Future::MicroService::Filter.with(
        segment_class_id: hierarchy_stq.id,
        segment_ids: [hierarchy_option.id],
        self_report: false
      )
    ]
  end

  its(:filters) { is_expected.to eq(direct_filters) }
  its(:hierarchy_filters) { is_expected.to eq(hierarchy_filters) }
  its(:serialize) { is_expected.to eq(serialized_filters) }

  context "when there are multiple anchors" do
    let(:context) { instance_double(Context, filters: report_filters, anchors: [report_filter_1, report_filter_3], leader_filter: nil) }
    let(:report_filter_3) { instance_double(ReportFilter, survey_to_question: hierarchy_stq, option: hierarchy_option, hierarchy?: false) }
    let(:serialized_filters) do
      {
        filters: {stq.id.to_s => [option1.id.to_s, option2.id.to_s]},
        hierarchy_filters: {}
      }
    end
  end

  context "when there are no filters" do
    let(:serialized_filters) do
      {
        filters: {},
        hierarchy_filters: {}
      }
    end

    let(:context) { instance_double(Context, filters: [], anchors: nil, leader_filter: nil) }
    its(:serialize) { is_expected.to eq(serialized_filters) }
  end

  context "when it is a roll up report" do
    let(:context) { instance_double(Context, filters: [report_filter_1], anchors: [report_filter_2], leader_filter: nil) }
    let(:full_reporting_line) { true }
    it "treats the anchor as a hierarchy filter" do
      expect(subject.hierarchy_filters).to eq([Future::MicroService::Filter.with(
        segment_class_id: report_filter_2.survey_to_question.id,
        segment_ids: [report_filter_2.option.id],
        self_report: false
      )])
    end

    context "with leader selection" do
      let(:context) { instance_double(Context, filters: [report_filter_1], anchors: [report_filter_2], leader_filter: hierarchy_filter) }
      it "returns the selected leader as hierarchy filter" do
        expect(subject.hierarchy_filters).to eq([Future::MicroService::Filter.with(
          segment_class_id: hierarchy_filter.survey_to_question.id,
          segment_ids: [hierarchy_filter.option.id],
          self_report: false
        )])
      end
    end
  end
end

RSpec.describe Future::MicroService::Filter do
  subject { Future::MicroService::Filter.for_report_filter(report_filter) }

  let(:report_filter) { instance_double(ReportFilter, survey_to_question: stq, option: option, hierarchy?: false) }
  let(:stq) { instance_double(SurveyToQuestion, id: "stq_id", self_report?: true) }
  let(:option) { instance_double(SelectOption, id: "option_id") }

  its(:segment_class_id) { is_expected.to eq("stq_id") }
  its(:segment_ids) { is_expected.to eq(["option_id"]) }
  its(:self_report) { is_expected.to be true }
end
