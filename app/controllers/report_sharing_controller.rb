class ReportSharingController < BaseReportsController
  include LinksHelper
  include ReportSharingHelper
  include DemographicNameHelper
  include ReportSharing::Queries::MultiDemographicFeatureFlag
  include ReportScopeHelper

  component :survey

  HR_BUSINESS_PARTNER_ENDPOINTS = [:index, :update_grants, :report_consumers_count, :notify_counts, :available_consumers, :show].freeze

  allows_access_to :all_authenticated_users

  before_action except: HR_BUSINESS_PARTNER_ENDPOINTS do
    render_forbidden unless can_administer_survey_reports?(survey, current_user)
  end

  before_action only: HR_BUSINESS_PARTNER_ENDPOINTS do
    next if can_administer_survey_reports?(survey, current_user)
    render_forbidden unless !survey.archived && Authorization.permitted?(
      user_id: current_user.aggregate_id,
      resource_id: account&.aggregate_id,
      permission: Account::Permissions::SHARE_SURVEY_REPORTS
    )
  end

  # This handles the case where all of the checkboxes have been cleared,
  # in which case the parameter is absent.
  before_action only: [:create, :update] do
    params[:report][:filter_demographic_stq_ids] ||= []
    params[:report][:report_scope] ||= []
    render_bad_request(error_title: "Multi demographics disabled", error: "Attempting to create a multi demographic report when feature is disabled") if is_multi_demographic_request? && !has_customer_access_to_multi_demographic_reports?(user_id: current_user.aggregate_id, survey_id: survey.aggregate_id, account_id: account&.aggregate_id)
  end

  allows_jwt_authentication
  skip_before_action :verify_authenticity_token, only: [:create, :update]

  expose(:reports) { survey.reports.asc("name") }
  expose(:report)
  expose(:report_view_options) { report.report_view_options }
  expose(:report_sharing_view_options) { report.report_sharing_view_options }
  expose(:allowed_filters) { Comments::Filters.new(survey, report) }
  expose(:report_sharing_titleblock_props) { titleblock_props }
  expose(:report_sharing_recipients_helper) { ::ActionFrameworks::Notifications::ReportSharingRecipientsHelper.new(report) }

  def index
  end

  def show
    render json: demographic_options(report, survey)
  end

  def report_consumers_count
    report = Report.find(params[:report_id])
    render json: {"count" => report.consumers.size}
  end

  def create
    report.survey_id = params[:survey_id]
    create_or_update
  end

  def update
    create_or_update
  end

  def simplified_report_configuration_index
    return render_forbidden if report.multi_demographic_report? && !has_customer_access_to_multi_demographic_reports?(user_id: current_user.aggregate_id, survey_id: survey.aggregate_id, account_id: account&.aggregate_id)

    render "report_sharing/index"
  end

  def update_grants
    consumer_ids = consumer_list(params[:consumer_ids])
    report_owner_id = consumer_list(params[:report_owners_ids])&.first

    select_option_ids = select_option_ids_from_params(params[:select_option_id])
    select_option_ids_valid = select_option_ids.all? { |so_id| BSON::ObjectId.legal?(so_id) }
    return render_bad_request(error_title: "Invalid select option ids", error: "Parameter select_option_id is invalid", invalid_entities: select_option_ids) unless select_option_ids_valid

    Reports::UpdateGrants.set_viewers(
      report: report,
      consumer_ids: consumer_ids - [report_owner_id],
      select_option_ids: select_option_ids
    )

    Reports::UpdateGrants.set_report_owner(
      report: report,
      report_owner_id: report_owner_id,
      select_option_ids: select_option_ids
    )

    render json: view_permissions(select_option_ids)
  end

  def bulk_remove_grants_progress
    return render_not_found unless bulk_remove_grants_enabled?

    process_entity = bulk_remove_grants_process_entity(params[:bulk_remove_grants_aggregate_id])

    render_bulk_remove_progress(process_entity)
  end

  def bulk_remove_grants_process_entity(aggregate_id)
    Domains::MurmurGeneral.database(:general_db)[:bulk_remove_grants_process].first(aggregate_id: aggregate_id)
  end

  def render_bulk_remove_progress(process_entity)
    if process_entity[:failed_at].present?
      render_bulk_remove_grants_error
    elsif process_entity[:progress] == 100
      render_bulk_remove_grants_done(process_entity)
    else
      render_bulk_remove_grants_running(process_entity)
    end
  end

  def render_bulk_remove_grants_running(process_entity)
    render json: {
      status: "running",
      progress: process_entity[:progress]
    }
  end

  def render_bulk_remove_grants_done(process_entity)
    render json: {
      status: "done",
      progress: process_entity[:progress]
    }
  end

  def render_bulk_remove_grants_error
    render json: {status: "error"}
  end

  def bulk_remove_grants
    return render_not_found unless bulk_remove_grants_enabled?

    reports = survey.reports.where(is_sample: false).to_a.reject { |report| report.deleted? }

    process_aggregate_id = BulkRemoveReportGrants::Commands::CreateBulkRemoveGrantsProcess.new(reports: reports, survey: survey, executor_id: current_user.aggregate_id).call

    Jobs::BulkRemoveReportGrantsJob.enqueue(reports: reports, bulk_remove_grants_process_id: process_aggregate_id)

    render json: {bulk_remove_grants_process_id: process_aggregate_id}
  end

  def destroy
    report.destroy
    head :ok
  end

  def notify_counts
    render json: {
      new: report.published? ? report_sharing_recipients_helper.recipients_to_grants_count(non_emailed_grants_only: true) : 0,
      all: report.published? ? report_sharing_recipients_helper.recipients_to_grants_count(non_emailed_grants_only: false) : 0,
      allReportViewersCount: all_report_viewers_count,
      allReportOwnersCount: all_report_owners_count
    }
  end

  def available_consumers
    render json: available_report_consumers
  end

  private

  def is_multi_demographic_request?
    report_parameters[:report_scope].present?
  end

  def bulk_remove_grants_enabled?
    FeatureFlags::Queries::ValueForSurvey.new.call(
      survey: survey,
      flag_name: FeatureFlags::Flags::SURVEY_REPORTING_SHOW_REMOVE_ALL_REPORT_VIEWERS_BUTTON,
      fallback_value: false
    )
  end

  def create_or_update
    parameters = report_parameters
    if Services::SharedReportsService.report_exists?(survey.id, parameters[:name]) && report.name != parameters[:name]
      return render_duplicate_report_name
    elsif is_multi_demographic_request?
      invalid_stqs = report_scope_duplicate_demographics(params.dig(:report, :report_scope))
      return render_bad_request(error_title: "Duplicate demographics", error: "The same demographic cannot be selected more than once", invalid_entities: invalid_stqs) if invalid_stqs.any?

      number_of_demographics = parameters[:report_scope].keys.count
      return render_max_demographics_exceeded(number_of_demographics) if number_of_demographics > MAX_DEMOGRAPHICS
      return render_min_demographics_not_met(number_of_demographics) if number_of_demographics < MIN_DEMOGRAPHICS

      invalid_stqs = report_scope_missing_select_options(parameters[:report_scope])
      return render_bad_request(error_title: "Missing select options", error: "One or more demographics in report_scope are missing select options", invalid_entities: invalid_stqs) if invalid_stqs.any?
      invalid_select_options = report_scope_invalid_select_options(parameters[:report_scope], survey)
      return render_invalid_select_options(invalid_select_options) if invalid_select_options.any?
    end

    saved_report =
      if create?
        Services::SharedReportsService.create_report(survey, parameters, true)
      else
        Services::SharedReportsService.update_report(report, parameters)
      end

    if saved_report.invalid?
      duplicate_select_options = saved_report.errors.details[:report_scope]&.find { |err| err[:type] == :duplicate_select_option }
      return render_bad_request(error_title: duplicate_select_options[:title], error: duplicate_select_options[:message], error_type: duplicate_select_options[:type], invalid_entities: duplicate_select_options[:select_option_id], status: :unprocessable_entity) if duplicate_select_options

      render_bad_request(error_title: "Invalid report", error: saved_report.errors.full_messages.join("\n"))
    else
      render json: helpers.demographic_report_props(
        report: saved_report,
        survey: survey,
        stqs: survey.active_demographics,
        user: current_user
      )
    end
  end

  def create?
    action_name == "create"
  end

  def render_duplicate_report_name
    # The error title here is used in the frontend to focus and scroll to the name input component. It should really be a shared variable between the fe/be.
    # If you need to change this, please also change the condition in FilteredReportForm.js
    render_bad_request(error_title: "Duplicate name", error: "The name \"#{report_parameters[:name]}\" is already taken. Please choose a different name.")
  end

  def render_max_demographics_exceeded(number_of_demographics)
    render_bad_request(error_title: "Exceeds maximum number of demographics", error: "Number of demographics provided: #{number_of_demographics} Maximum number of demographics: #{MAX_DEMOGRAPHICS}")
  end

  def render_min_demographics_not_met(number_of_demographics)
    render_bad_request(error_title: "Number of demographics insufficient", error: "Number of demographics provided: #{number_of_demographics} Minimum number of demographics #{MIN_DEMOGRAPHICS}")
  end

  def render_invalid_select_options(invalid_select_options)
    render_bad_request(error_title: "Invalid select options", error: "The one or more select options are invalid", invalid_entities: invalid_select_options)
  end

  def render_bad_request(error_title:, error:, error_type: nil, status: :bad_request, invalid_entities: nil)
    payload = {
      error: error,
      errorTitle: error_title
    }

    payload[:invalidEntities] = invalid_entities if invalid_entities
    payload[:errorType] = error_type if error_type

    render json: payload, status: status
  end

  def consumer_list(consumer_ids)
    if consumer_ids.is_a?(String)
      # Select2 sends multiple selects backed by a
      # hidden input as a comma-separated string
      consumer_ids = consumer_ids.split(",")
    end

    consumer_ids
  end

  def all_report_viewers_count
    if survey.send_engagement_emails?
      return report_sharing_recipients_helper.recipients_to_viewer_grants_count(non_emailed_grants_only: false)
    end

    report_sharing_recipients_helper.all_report_viewers_count
  end

  def all_report_owners_count
    if survey.send_engagement_emails?
      return report_sharing_recipients_helper.recipients_to_report_owner_grants_count(non_emailed_grants_only: false)
    end

    report_sharing_recipients_helper.all_report_owners_count
  end

  def available_report_consumers
    send_engagement_emails_enabled = survey.send_engagement_emails?

    projection_items = [:id, :name]
    projection_items.append(:email) if send_engagement_emails_enabled

    survey
      .available_report_consumers
      .order_by(:name_sort.asc)
      .only(*projection_items)
      .to_a
      .each_with_object({}) do |person, result|
        result[person.id.to_s] =
          if send_engagement_emails_enabled
            {n: person.name, e: person.email}
          else
            {n: person.name}
          end
      end
  end

  def person_view(person)
    {
      email: person.email,
      id: person.id.to_s,
      name: person.name
    }
  end

  def report_parameters
    params.require(:report)
      .permit(
        :name,
        :description,
        :show_comments,
        :show_ai_comment_summaries,
        :show_ai_comment_comparisons,
        {filter_demographic_stq_ids: []},
        :report_view,
        :base_demographic_stq_id,
        {report_scope: [
          :stq_id,
          select_option_ids: []
        ]},
        :direct_reports_only,
        :leader_select_enabled,
        :af_enabled
      )
      .to_h
      .tap do |hash|
        hash.merge!(
          report_scope: transform_api_report_scope(hash[:report_scope])
        )
      end
  end

  def demographic_options(report, survey)
    if report.all_results_report?
      all_results_demographic_options_details(report, survey)
    else
      demographic_option_details(report, survey)
    end
  end

  def demo_report_path(report, select_option_ids = [])
    report.can_preview? ?
      report_path(DemoReportAccessGrant.new(report.survey, report, select_option_ids.first, select_option_ids), current_user) :
      nil
  end

  def demographic_option_details(report, survey)
    demographic_report_select_options(survey, report).map { |select_options|
      select_option_ids = Array.wrap(select_options).map(&:id)
      {
        # opaque ID passed back to update_grants, etc.
        "id" => select_option_ids.join("_"),
        "demoReportUrl" => demo_report_path(report, select_option_ids),
        "initialConsumers" => selected_consumers(select_option_ids),
        "initialReportOwnerConsumers" => selected_report_owners(select_option_ids),
        "name" => get_name_or_label(is_hierarchy: report.base_demographic_stq&.employee_hierarchy_id? || false, select_options: select_options, account_id: survey.account_id),
        "submitted" => submitted_responses(report, select_option_ids),
        "total" => total_responses(report, select_option_ids),
        "updateConsumersUrl" => survey_report_update_grants_path(survey, report),
        "viewPermissions" => view_permissions(select_option_ids)
      }
    }.sort_by { |key| key["name"]&.downcase }
  end

  def all_results_demographic_options_details(report, survey)
    [
      {
        "id" => nil,
        "demoReportUrl" => demo_report_path(report),
        "initialConsumers" => selected_consumers(nil),
        "initialReportOwnerConsumers" => selected_report_owners(nil),
        "name" => report.name,
        "submitted" => all_results_submitted(survey),
        "total" => all_results_total(survey),
        "updateConsumersUrl" => survey_report_update_grants_path(survey, report),
        "viewPermissions" => view_permissions
      }
    ]
  end

  def all_results_submitted(survey)
    return survey.responses.real.completed.count if survey[:type] == :exit

    survey.responses.real.submitted.count
  end

  def all_results_total(survey)
    survey.design? ? survey.survey_participants.count : survey.responses.real.count
  end

  def selected_consumers(select_option_ids)
    report_sharing_recipients_helper
      .selected_viewers(select_option_ids)
      .map { |person| person_view(person) }
  end

  def selected_report_owners(select_option_ids)
    report_sharing_recipients_helper
      .selected_report_owners(select_option_ids)
      .map { |person| person_view(person) }
  end

  def submitted_responses(report, select_option_ids)
    return 0 if survey.status == :design

    report.select_options_population.fetch(select_option_ids, {}).fetch(:submitted, 0)
  end

  def total_responses(report, select_option_ids)
    report.select_options_population.fetch(select_option_ids, {}).fetch(:total, 0)
  end

  def construct_label_for_button
    report.new_record? ? "Save" : "Update"
  end

  def view_permissions(select_option_ids = nil)
    {
      canPreviewReport: check_report_privileges(select_option_ids)
    }
  end

  def check_report_privileges(select_option_ids)
    return true if current_user.can_administer_survey?(survey) || can_preview_insight_report?(select_option_ids)

    false
  end

  def can_preview_insight_report?(select_option_ids)
    return false unless has_hr_business_partner_role?

    if select_option_ids.blank?
      user_has_access_to_report?
    else
      user_has_access_to_sub_report?(select_option_ids)
    end
  end

  def user_has_access_to_report?
    report.access_grants.where(report_consumer_id: current_user.id).exists?
  end

  def user_has_access_to_sub_report?(select_option_ids)
    report.access_grants.with_select_option_ids(select_option_ids).where(report_consumer_id: current_user.id).exists?
  end
end
