# noinspection RbsMissingTypeSignature
class Trend::Api<PERSON>ontroller < ReportsController
  include Scientist
  include VerifySameO<PERSON>in

  replace_csrf_with_origin_check
  allows_jwt_authentication

  # At the time of writing, Trend report have the same access with insight report
  REPORT_TYPE = ReportDataTypes::TREND
  LOGGER = Splunk::Logger.new(Rails.logger)

  def graphql
    render json: trend
  end

  private

  def trend
    queries = query_params.map { |param|
      {
        operation_name: param[:operationName],
        query: param[:query],
        variables: param[:variables].to_h,
        context: {authorized_survey: survey, filters: filters, hierarchy_privacy: hierarchy_privacy?}
      }
    }

    # if we failed to use trend 2024 for any reason, we will fallback to the old trend
    begin
      return trend_2024(queries) if trend2024_enabled?
    rescue => e
      debug(error: e)
      LOGGER.log(
        app: "murmur",
        module: "reporting_api.trend_api.post",
        error: e
      )
    end

    duration, result = Trend2024::Science::Experiment.time {
      Trend::Schema.multiplex(queries)
    }

    trend_2024_experiment(duration, queries, result)

    result
  end

  def trend_2024_experiment(duration, queries, result)
    Trend2024::Science::Experiment.test(
      user: current_user,
      account: account,
      queries: queries,
      survey_id: survey.id.to_s,
      filters: filters,
      filter_configuration: filter_configuration,
      hierarchy_privacy: hierarchy_privacy?,
      full_reporting_line: full_reporting_line?,
      control_result: result,
      control_duration: duration,
      locale: locale
    )
  end

  def trend2024_enabled?
    FeatureFlags::Queries::ValueForUser.new.call(
      user: current_user,
      account: account,
      flag_name: FeatureFlags::Flags::SURVEY_REPORTING_TREND_2024_ENABLED,
      fallback_value: false
    )
  end

  def trend_2024(queries)
    Trend2024::Adapters::GraphQLApi.new.call(
      queries: queries,
      survey_id: survey.id,
      filters: filters,
      filter_configuration: filter_configuration,
      hierarchy_privacy: hierarchy_privacy?,
      full_reporting_line: full_reporting_line?
    )
  end

  # @return [Hash] with keys `filters` and `hierarchy_filters`.  See [MicroService#serialize] for more information.
  def filters
    if has_access_to_view_multi_demographic_reports?(
      user_id: current_user.aggregate_id,
      account_id: current_user.account.aggregate_id,
      survey_id: survey.aggregate_id
    )
      Future::MicroService::Filters.for_context(context, full_reporting_line?).serialize
    else
      MicroService::Filters.for_context(context, full_reporting_line?).serialize
    end
  end

  def filter_configuration
    ::Query::FilterConfiguration.for_context(context, context.filters(false), full_reporting_line: context.full_reporting_line)
  end

  def query_params
    params.require(:_json).map do |param|
      param.permit(:operationName, :query, variables: {})
    end
  end

  def establish_legacy_report_scope
    @scope = ManagerInsightReportPermission
    return unless report_scope(%i[manager_insight_id survey_id dashboard_id].detect { |id| params[id].present? } || :id)

    unless current_user.can_access_survey?(@model, @scope)
      render_forbidden
      return
    end
    determine_anchor
  end

  def debug(**args)
    if Rails.env.development?
      args.each do |key, value|
        puts "################## #{key} ##################"
        puts value.awesome_inspect
        puts "################## #{key} ##################"
      end
    end
  end
end
