class ApplicationController < ActionController::Base
  before_action :impersonate_any_user_test_hook
  before_action :impersonate_anonymous_user_test_hook

  include Controlamp::LoggingHelper
  include Controlamp::Base
  include Controlamp::Component
  component :survey

  UserNotFoundError = Class.new(StandardError)
  RealUserNotFoundError = Class.new(StandardError)

  # Defined before the includes to ensure that StandardError rescue is matched last
  unless Rails.env.development?
    rescue_from StandardError, with: :render_internal_server_error
    rescue_from(Mongoid::Errors::DocumentNotFound) { |e| render_not_found(e) }
    rescue_from(Mongoid::Errors::InvalidFind) { |e| render_not_found(e) }
    rescue_from(ActionController::UnknownFormat) { |_| render status: :not_acceptable }
  end

  # Normal handling for an internal server error is to raise the error and let <PERSON><PERSON> handle it
  # Override this if you wish to handle errors differently, such as with a JSON response body.
  def render_internal_server_error(e)
    raise(e)
  end

  include BugsnagIntegration
  include PdfHelper
  include ProtectedController
  include ApplicationProps
  include I18nHelper
  include JwtAuthentication
  include AmplitudeDeviceSession
  include Trackable

  allows_jwt_authentication
  layout nil
  helper_method :current_user, :devise_current_user, :masquerading?, :masquerade_identity, :authorization, :pdf?, :param_to_sym, :switch_locale, :pdf_orientation, :pdf_page_size
  protect_from_forgery with: :exception

  tag_requests_with feature: :unknown

  def request_authenticity_tokens
    # Sometimes the token is added to the X-CSRF-Token header twice, so we handle
    # this by splitting the header on the ", " delimiter used by XMLHttpRequest.
    [form_authenticity_param] | Array(request.x_csrf_token&.split(", "))
  end

  def unmask_token_for_logging(encoded_masked_token)
    # Temporary code to get telemetry on CSRF validation failures
    masked_token = Base64.strict_decode64(encoded_masked_token)
    one_time_pad = masked_token[0...AUTHENTICITY_TOKEN_LENGTH]
    encrypted_csrf_token = masked_token[AUTHENTICITY_TOKEN_LENGTH..-1]
    Base64.strict_encode64(xor_byte_strings(one_time_pad, encrypted_csrf_token))
  rescue Exception # rubocop:disable Lint/RescueException
    # Don't cause exceptions by trying to log tokens
    nil
  end

  def handle_unverified_request
    # Temporary code to get telemetry on CSRF validation failures
    logger = Splunk::Logger.new(Rails.logger)
    logger.log(
      app: "murmur",
      msg: "Can't verify CSRF token authenticity.",
      path: request.path,
      controller: controller_name,
      action: action_name,
      form_token: unmask_token_for_logging(form_authenticity_param),
      header_token: unmask_token_for_logging(Array(request.x_csrf_token&.split(", ")).first),
      expected_token: session[:_csrf_token],
      is_logged_in: current_user.present? ? "yes" : "no"
    )
    super # continue to handle unverified request as per the protect_from_forgery strategy
  end

  before_action :check_for_hack_attempt
  skip_after_action :intercom_rails_auto_include

  expose(:account) { current_user.try(:account) }
  expose(:context) { nil }
  expose(:banner) { Murmur::Banner.new(browser, cookies, pdf?) }
  expose(:current_url_with_params) { request.original_url }

  # Exposed so that we can look for `survey` in the log
  # payload without worrying that it might be undefined.
  expose(:survey) { nil }

  # Single before filter that handles some useful tasks:
  # - redirects HTTP GET requests to HTTPS
  # - if there is a user, adds it to the header for logging
  # - tolerant session fixation against a User-Agent
  before_action do
    # HTTPS Redirect
    if request.get? && !ssl? && request.subdomain != "assets"
      if Rails.env.production? || (Rails.env.staging? && request.host != "127.0.0.1")
        return redirect_to("https://" + request.host + request.fullpath)
      end
    end

    # Additional security headers
    # response.headers["Content-Security-Policy"] = "script-src 'self' 'unsafe-inline' #{URI(Rails.application.config.asset_host).host} s.adroll.com"
    response.headers["Strict-Transport-Security"] = "max-age=16070400; includeSubDomains"

    # This is required as locale is set in other requests and persists on
    # the ruby process. This sets the default which can be changed down the
    # chain.
    # Attempt to use the explictly selected locale
    I18n.locale = params[:locale].present? ? params[:locale] : I18n.default_locale
  rescue I18n::InvalidLocale
    I18n.locale = I18n.default_locale
  rescue => e
    # Don't worry too much.. This shouldn't be fatal
    Rails.logger.error "ApplicationController utility before_actions due to '#{e.message}'"
  end

  before_action :show_privacy_notice_for_new_user

  def initialize
    super
    @banners = 0
    @meta_tags = {}
  end

  def check_for_hack_attempt
    is_masquerading = if use_jwt
      jwt_user_id != jwt_real_user_id
    else
      session[:masquerade_user].present?
    end

    if is_masquerading
      # If the user has masquerade_user in their session, it must be a valid user that matches the salts, and they have access to access.
      current_user = devise_current_user
      if current_user.present? && !current_user.superuser?
        # current user is not a superuser
        possible_hack_attempt = true
      else
        masqueraded_user = masquerade_identity
        if masqueraded_user.nil? || masqueraded_user.superuser?
          # masqueraded user does not exist OR
          # current user tries to masquerade as another superuser
          possible_hack_attempt = true
        end
      end

      # if we detect something suspicious, reset their session, log the error, and kick them out to a forbidden page.
      handle_possible_hack_attempt(current_user) if possible_hack_attempt
    end

    true
  end

  def handle_possible_hack_attempt(user)
    user_string = user.present? ? user.to_s : "an anonymous user"
    Rails.logger.error "Resetting session because #{user_string} invalidly attempted to masquerade as user #{session[:masquerade_user].inspect}"
    reset_session
    render_forbidden && return
  end

  def user_id
    devise_current_user&.id&.to_s
  end

  def user_aggregate_id
    devise_current_user&.aggregate_id
  end

  # This adds information to be used when logging (see application.rb)
  def append_info_to_payload(payload)
    super

    payload[:remote_ip] = request.remote_ip
    payload[:masquerade_user_aggregate_id] = current_user.aggregate_id if masquerading?
    # The session ID is obfuscated:
    # We can use it to correlate log events without exposing it.

    # referring to https://my.diffend.io/gems/actionpack/*******/5.2.5#d2h-670634-430.
    # In some cases session.id is a String type and in others it is a Rack::Session::SessionId instance.
    # Investigation shows it being a String in production mode because it resolves from action_dispatch/request/session.rb
    # but in test mode it resolves from rack/session/abstract/id.rb.
    if session.try(:id).present?
      session_id = session.id.is_a?(Rack::Session::SessionId) ? Digest::MD5.hexdigest(session.id.public_id) : Digest::MD5.hexdigest(session.id)
      payload[:session_id] = session_id
    end
    payload[:request_id] = request.uuid

    payload[:user_aggregate_id] = user_aggregate_id
    payload[:user_id] = user_id

    account_for_logging =
      begin
        account
      rescue
        nil
      end

    if account_for_logging
      payload[:account_subdomain] = account_for_logging.subdomain
      payload[:account_id] = account_for_logging.id.to_s
      payload[:account_aggregate_id] = account_for_logging.aggregate_id
    end

    payload[:survey_name] =
      begin
        survey.try(:name)
      rescue
        nil
      end
  end

  def process_action(*args)
    super
  rescue UserNotFoundError, RealUserNotFoundError, JwtAuthentication::JWTDecoderError => e
    Rails.logger.warn("action='Authentication Failure' type='#{e.class.name}' message='#{e.message}'")
    Sentry.capture_exception(e)
    render_unauthorized
  end

  def param_to_sym(key, valid_symbols, default_value = nil)
    # Using .to_s to block params[key]from returning an unexpected array
    # Ie, a URL parameter hack attempt
    params[key].to_s.try(:to_sym_safely, valid_symbols) || default_value
  end

  # Overriding current_user to allow superusers (and only superusers) to masquerade as another user.
  # If at any stage you want to know who the 'real' logged in user is, call devise_current_user.
  def current_user
    @current_user = masquerading? ? masquerade_identity : devise_current_user
  end

  # Is the current user masquerading as another user?
  def masquerading?
    masquerade_identity.present?
  end

  # Checks whether JWT authentication has been enabled in the controller (see ProtectedController#allows_jwt_authentication)
  # and whether a JWT exists in the request.
  def use_jwt
    jwt_authentication_enabled && jwt.present?
  end

  # The real logged in user as determined by Devise. If Devise doesn't return a user, it falls back
  # to the real user determined from a JWT, if one exists on the request.
  def devise_current_user
    @_devise_current_user ||= begin
      subdomain = request.subdomains.first
      if fusionauth_jwt_post_signin_enabled_by_subdomain?(subdomain)
        real_user_from_jwt
      else
        real_user_from_jwt || warden.user
      end
    end
  end

  # Returns the real current user as determined by a supplied JWT. Returns nil JWT authentication isn't enabled/no JWT
  # supplied.
  def real_user_from_jwt
    @_real_user_from_jwt ||= begin
      return nil unless use_jwt

      person = ::Person.where(aggregate_id: jwt_real_user_id).first
      raise RealUserNotFoundError if person.nil?
      person
    end
  end

  # The user the real logged in user is currently masquerading as
  def masquerade_identity
    # Use `defined?` as `nil` is a valid value for `masquerade_identity`
    return @_masquerade_identity if defined?(@_masquerade_identity)

    @_masquerade_identity = if use_jwt && jwt_user_id != jwt_real_user_id
      user_from_jwt
    else
      user_id, token = session[:masquerade_user]
      if user_id.present? && token.present?
        user = ::Person.where(id: user_id).first
        user if user.present? && user.masquerade_token == token
      end
    end
  end

  # Returns the effective current user as determined by a supplied JWT. If masquerading, this will be different to the
  # real user. Returns nil JWT authentication isn't enabled/no JWT supplied.
  def user_from_jwt
    @_user_from_jwt ||= begin
      return nil unless use_jwt

      person = ::Person.where(aggregate_id: jwt_user_id).first
      raise UserNotFoundError if person.nil?
      person
    end
  end

  def stop_masquerading
    return unless masquerading?

    begin
      mu = masquerade_identity
      if mu.present?
        Rails.logger.info "User \"#{devise_current_user}\" has stopped masquerading as \"#{mu}\"."
      else
        Rails.logger.info "User \"#{devise_current_user}\" has stopped masquerading."
      end
    ensure
      session[:masquerade_user] = nil
    end
  end

  # Adds in a "locale" parameter to all helper methods
  # This will only be added if the locale is different
  # from the default.
  # FUTURE: Right now the default is :en - however, it would make more
  # sense to drive this off the context. e.g. for single non-english
  # accounts.
  def default_url_options(opts = {})
    if params[:locale].present? || I18n.locale != I18n.default_locale
      opts[:locale] = I18n.locale
    end
    opts
  end

  # Can be used as a before_action to set locale from the URL
  def switch_locale
    set_locale(params[:locale])
  end

  def set_locale(locale)
    I18n.locale = locale || I18n.default_locale
  end

  def construct_date_time
    DateMethods.localise_date_to_time(account.time_zone, params[:date], nil, params[:time].to_i) if params[:date].present?
  end

  # Returns http or https depending on environment.
  def protocol
    Rails.env.production? ? "https" : "http"
  end

  # No Cache Headers, as defined by Barricaded security review
  def no_cache
    response.headers["Cache-Control"] = "no-cache, no-store, private, max-age=0, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "Fri, 01 Jan 1990 00:00:00 GMT"
  end

  # Calls present? on all the objects in the splat and
  # returns the logical AND (&&) of the result.
  def present?(*args)
    args.each { |v| return false if v.blank? }
    true
  end

  # Redirects to the home page
  def redirect_home
    redirect_to("/")
  end

  # Hook into Devise to remember the User Agent
  def sign_in(resource, *args)
    resource.try(:cache_failed_attempts)
    ret = super
    user_agent = browser.ua
    session[:user_agent] = user_agent.match?(/chromeframe/) ? "" : user_agent
    ret
  end

  def impersonate_any_user_test_hook
    # only acts as a hook for testing, no-op for any other environment
  end

  def impersonate_anonymous_user_test_hook
    # only acts as a hook for testing, no-op for any other environment
  end

  # Jimmied SSL check that uses the underlying Rails check, as
  # well as a custom one required to support CloudFront. This
  # *shouldn't* be necessary, but it's not behaving as expected.
  def ssl?
    request.ssl? || (request.headers["HTTP_X_FORWARDED_PORT"] == "443")
  end

  def substitute(key, changes = {})
    result = I18n.translate(key)
    changes.each_key do |k|
      result.gsub!(/%#{k}%/i, changes[k])
    end
    result
  end

  def show_privacy_notice_for_new_user
    return unless devise_current_user
    return if devise_current_user.account.free?
    return if has_accepted_terms_and_conditions?

    redirect_to terms_and_conditions_url(redirect: params[:redirect], host: current_user.account.domain)
  end

  # Rails' implementation of request.subdomain makes some assumptions about how
  # many labels exist in the TLD component of the request's hostname. Because
  # we're now deploying to eu.cultureamp.com, we need to compensate.
  def account_label_from_hostname
    request.subdomain(culture_amp_domain_tld_label_count)
  end

  def culture_amp_domain_tld_label_count
    @culture_amp_domain_tld_label_count ||= Murmur::CULTURE_AMP_DOMAIN.split(".").length - 1
  end

  protected

  # Use HTTP status 303 (See Other) to ensure that the redirect uses a GET request.
  # Otherwise, some browsers might retain the original method for the redirected URL,
  # which can lead to double POSTs, double DELETEs, etc.
  #
  # http://en.wikipedia.org/wiki/HTTP_303
  def see_other(*args)
    redirect_to(*args, status: :see_other)
  end

  def has_accepted_terms_and_conditions?(user = devise_current_user)
    user&.terms_and_conditions_accepted
  end

  def render_interstitial
    details = ViewModels::Interstitial.new(
      user: devise_current_user,
      account: devise_current_user.account,
      ip: request.remote_ip
    )

    render("privacy/interstitial", layout: "splash_capture", locals: {details: details})
  end

  def welcome_message(user)
    "Welcome #{CGI.escapeHTML(user.name)}. Your last login was on <time class='last-signed-in-at' datetime='#{user.last_sign_in_at}'>#{user.last_sign_in_at}</time>."
  end

  def survey_service
    @_survey_service ||= Services::SurveyService.new
  end

  def employee_service
    @_employee_service ||= Services::EmployeeService.new
  end

  def demographic_service
    @_demographic_service ||= Services::DemographicService.new
  end

  def authorization
    @authorization ||= Authorization.new(permit_provider: permit_provider)
  end

  private

  # The default permit provider is the mongo Permit collection. This means that authz checks
  # will first check the Permit collection for an explicitly granted permission and then fall
  # back to checking access by roles.
  # If you want to check permits from the permissions service (for either WRITE_COMMENT_REPLIES
  # or CREATE_SURVEY) then override this method by including `PermissionsServicePermits` in
  # your controller.
  def permit_provider
    Authorization::Permit
  end

  def fusionauth_jwt_post_signin_enabled_by_subdomain?(subdomain)
    FeatureFlags::Queries::ValueForContext.new.call(
      flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN,
      subdomain: subdomain,
      fallback_value: false
    )
  end
end
