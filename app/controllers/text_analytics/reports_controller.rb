class TextAnalytics::ReportsController < ReportsController
  component :report
  REPORT_TYPE = ReportDataTypes::TEXT_ANALYTICS
  include MultiFormatReport

  expose(:report_data) do
    {
      reportType: ReportDataTypes::TEXT_ANALYTICS,
      restrictedDemographicGroups: allowed_comment_filters,
      showTextAnalytics: survey.text_analytics_report_enabled?,
      commentTranslationsEnabled: survey.comment_translations_enabled?,
      excludeNoCommentClassifications: survey.exclude_no_comment_classifications?,
      title: I18n.t("navigation.comments"),
      backToComments: I18n.t("reports.titleblock.back_to_comments"),
      paths: {
        topics: remove_query_params(paths.comments),
        themes: remove_query_params(paths.comments_themes),
        saved: remove_query_params(paths.comments_saved),
        graphql: remove_query_params(paths.comments_graphql)
      }
    }
  end

  def index
    render_report
  end

  def graphql
    render json: graphql_response
  end

  def report_date_range
    return super unless survey.adhoc?

    @report_date_range ||= CommentsReportDateRange.calculate(
      time_zone: account.time_zone,
      survey: survey,
      params: params
    )
  end

  private

  def graphql_response
    queries = query_params.map { |param|
      {
        operation_name: param[:operationName],
        query: param[:query],
        variables: param[:variables].to_h,
        context: {
          filters: filters,
          survey: survey,
          current_user: current_user,
          report_paths: paths,
          participation_result: participation_result,
          filter_significance_result: filter_significance_result,
          request: request,
          report_data_service: report_data_service,
          comments_reporting_rules: reporting_rules,
          non_comments_reporting_rules: non_comments_reporting_rules,
          questions_for_reports: questions_for_reports
        }
      }
    }
    TextAnalytics::Schema.multiplex(queries)
  end

  # The main content of the report is subject to the CommentsRules for confidentiality.
  # @see ReportsController#filter_significance_result
  def reporting_rules
    Reporting::Confidentiality::CommentsRules.new.call(survey: survey)
  end

  # Some parts of the comments report renders questions metadata and scores that should
  # not be subject to the Comments reporting rules
  # @see #reporting_rules
  # @see app/graphql/text_analytics/queries/questions.rb
  def non_comments_reporting_rules
    Reporting::Confidentiality::StandardRules.new.call(survey: survey)
  end

  def filters
    filters = {demographic: serialized_filters}
    filters[:date_range] = {from: report_date_range.from.strftime("%Y-%m-%d"), to: report_date_range.to.strftime("%Y-%m-%d")} if survey.lifecycle?
    filters
  end

  def serialized_filters
    if has_access_to_view_multi_demographic_reports?(
      user_id: current_user.aggregate_id,
      account_id: current_user.account.aggregate_id,
      survey_id: survey.aggregate_id
    )
      Future::MicroService::Filters.for_context(context, full_reporting_line?).serialize
    else
      MicroService::Filters.for_context(context, full_reporting_line?).serialize
    end
  end

  def calculate_etag
    true
  end

  def query_params
    params.require(:_json).map do |param|
      param.permit(:operationName, :query, :from, :to, variables: {})
    end
  end

  def remove_query_params(url)
    return if url.blank?

    URI(url)
      .tap { |uri| uri.query = nil }
      .to_s
  end

  def establish_legacy_report_scope
    comments_report_scope
  end

  def ensure_can_view
    return true if can_view_comments?

    render_permission_failure
  end

  def can_view_comments?
    @can_view_comments ||=
      ::Policies::Comments.allow?(
        user: devise_current_user,
        survey: @model,
        report_access_grant: report_access_grant,
        report_sharing: report_sharing?,
        report_type: report_type
      )
  end
end
