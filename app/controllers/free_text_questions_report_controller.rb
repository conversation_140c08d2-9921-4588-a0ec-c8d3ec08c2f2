class FreeTextQuestionsReportController < ReportsController
  component :report
  REPORT_TYPE = :free_text_questions_report
  include MultiFormatReport

  expose(:report_data) { free_text_questions_report_data }

  def index
    render_report
  end

  private

  def reporting_rules
    Reporting::Confidentiality::StandardRules.new.call(
      survey: survey
    )
  end

  def free_text_questions_report_data
    if questions_result.error?
      return Props::QuestionsReportProps.error_props
    end

    Props::FreeTextQuestionsReportProps.new(
      questions_result.data,
      free_text_questions,
      paths,
      report_access_grant,
      survey,
      show_comment_count?,
      other_question_props
    ).to_hash
  end

  def free_text_questions
    @_free_text_questions ||= questions_for_reports.free_text_stqs
  end

  def other_question_props
    Props::OtherQuestionProps.new(
      survey: survey,
      questions_for_reports: questions_for_reports,
      paths: paths,
      admin_report: admin_report?
    )
  end

  def questions_for_reports
    @_questions_for_reports ||= QuestionsForReports.new(survey)
  end

  def questions_result
    comments_reporting_service.result(free_text_questions.map(&:question_id).map(&:to_s))
  end

  def comments_reporting_service
    serialized_filters = has_access_to_view_multi_demographic_reports?(
      user_id: current_user.aggregate_id,
      account_id: current_user.account.aggregate_id,
      survey_id: survey.aggregate_id
    ) ? Future::MicroService::Filters.for_context(context, full_reporting_line?).serialize
    : MicroService::Filters.for_context(context, full_reporting_line?).serialize

    TextAnalytics::QuestionSummaries.new(
      survey: survey,
      filters: {demographic: serialized_filters},
      client: CommentsReportingService::Client.new(
        metadata: CommentsReportingService::RequestMetadata.build(
          request: request,
          user: current_user,
          survey: survey,
          account: account
        )
      ),
      questions_for_reports: questions_for_reports
    )
  end

  def calculate_etag
    true
  end
end
