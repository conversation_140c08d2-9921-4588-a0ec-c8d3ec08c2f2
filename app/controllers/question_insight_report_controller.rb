class QuestionInsightReportController < ReportsController
  component :report
  REPORT_TYPE = :question_insight
  @model_id_name = :survey_id

  include MultiFormatReport

  expose(:stq) { @model.survey_to_questions.find(params[:id]) }
  expose(:report_data) do
    if action_name == "show"
      question_insight_report_data
    elsif action_name == "index"
      questions_report_data
    end
  end

  # Index of all questions for the survey
  def index
    render_report
  end

  # Insight into a specific question
  def show
    render_report
  end

  def survey_reporting_api_question_insight
    return render_not_found if stq.blank?

    # expose assigns report_data with different data based on action_name
    # Here, we only want question_insight_report_data so we directly assign report_data with it
    self.report_data = question_insight_report_data
    render json: survey_reporting_api_report_data
  end

  private

  def reporting_rules
    Reporting::Confidentiality::StandardRules.new.call(
      survey: survey
    )
  end

  def question_insight_report_data
    question_result = report_data_service.question_insight_result(stq.id, @context.comparison_key, survey.max_report_demographic_spread_size)

    return Props::QuestionInsightReportProps.error_props if question_result.error?
    Props::QuestionInsightReportProps.new(
      question_result.data,
      stq,
      @context,
      paths,
      questions_for_reports.segment_classify_dimension_stqs,
      show_enps?,
      recommended_questions.data,
      admin_report?,
      show_comment_count?,
      reporting_rules: reporting_rules # FIXME: Insights INX-483. Added as part of https://cultureamp.atlassian.net/secure/RapidBoard.jspa?rapidView=53&projectKey=INX&modal=detail&selectedIssue=INX-483
    ).to_hash.merge(common_question_props)
  end

  def questions_report_data
    questions_result = report_data_service.questions_result(rating_questions.map(&:id), @context.comparison_key)
    recommended_questions_result = recommended_questions

    if questions_result.error? || recommended_questions_result.error?
      return Props::QuestionsReportProps.error_props
    end

    Props::QuestionsReportProps.new(
      survey,
      questions_result.data,
      recommended_questions_result.data,
      rating_questions,
      questions_for_reports,
      paths,
      reporting_rules.significance,
      admin_report?,
      show_comment_count?
    ).to_hash.merge(common_question_props).tap do |props|
      scores = question_focus_scores.data
      props[:ratingQuestions].each do |question|
        question[:focusScore] = scores[question[:id]]&.fetch(:score)
      end
    end
  end

  def show_enps?
    stq.question.code == survey.config(Configs::ENPS_QUESTION_CODE)
  end

  def establish_legacy_report_scope
    if action_name == "show"
      old_manager_insight_scope(DemographicReportPermission, :insight_report_id)
    else
      old_manager_insight_scope(DriverAnalysisReportPermission, :insight_report_id)
    end
  end

  def question_focus_scores
    if all_results_report?
      focus_engine.overall_focus_scores
    else
      focus_engine.segment_level_focus_scores
    end
  end

  def recommended_questions
    if all_results_report?
      focus_engine.overall_recommended_focus
    else
      focus_engine.segment_level_recommended_focus
    end
  end

  def focus_engine
    @_focus_engine ||=
      FocusEngine::Service.new(survey, focus_engine_report_data_service, questions_for_reports)
  end

  def common_question_props
    {
      isAllResultsReport: all_results_report?
    }
  end
end
